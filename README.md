# MOF - 企业信息查询移动应用

## 项目概述

MOF 是一个基于 React Native 开发的企业信息查询移动应用，主要提供企业搜索、招投标信息查询、企业图谱分析等功能。应用采用现代化的技术栈，包括 TypeScript、Zustand 状态管理、Alova 网络请求库、NativeWind 样式框架等。

## 核心功能

### 🔍 找客户
- **企业搜索**: 支持多维度企业信息搜索
- **招投标信息**: 实时招标公告查询和筛选
- **行业分类**: 按行业标签快速定位目标企业
- **智能推荐**: 基于用户行为的潜在客户推荐

### 🔗 企业图谱
- **关系网络**: 可视化展示企业关联关系
- **图谱分析**: 深度分析企业投资、合作关系
- **企业列表**: 分类展示不同类型企业
- **产品列表**: 企业产品信息查询

### ❤️ 关注功能
- **关注管理**: 收藏感兴趣的企业和项目
- **动态更新**: 实时推送关注对象的最新动态
- **分类管理**: 按类型管理关注内容

### 👤 个人中心
- **用户管理**: 个人信息编辑和设置
- **订单管理**: 查看购买记录和服务状态
- **帮助支持**: 用户协议、帮助文档、客服支持

## 技术架构

### 前端技术栈
- **React Native 0.80.1**: 跨平台移动应用开发框架
- **TypeScript 5.0.4**: 类型安全的 JavaScript 超集
- **React Navigation 7.x**: 导航路由管理
- **Zustand 5.0.6**: 轻量级状态管理库
- **NativeWind 4.1.23**: React Native 的 Tailwind CSS 实现
- **Alova 3.3.4**: 现代化的请求库，支持缓存和状态管理

### UI 组件库
- **React Native Vector Icons**: 图标库
- **React Native SVG**: SVG 图形支持
- **React Native Linear Gradient**: 渐变效果
- **React Native Reanimated**: 高性能动画库
- **React Native Gesture Handler**: 手势处理

### 数据可视化
- **ECharts 5.6.0**: 图表可视化库
- **@wuba/react-native-echarts**: React Native ECharts 适配器
- **@shopify/react-native-skia**: 高性能 2D 图形渲染

### 开发工具
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Jest**: 单元测试框架
- **Metro**: React Native 打包工具

## 项目结构

```
mof/
├── src/                          # 源代码目录
│   ├── api/                      # API 接口定义
│   │   ├── apiDefinitions.ts     # API 接口类型定义
│   │   ├── createApis.ts         # API 创建工具
│   │   └── index.ts              # API 实例配置
│   ├── assets/                   # 静态资源
│   │   ├── svg/                  # SVG 图标
│   │   └── *.png                 # 图片资源
│   ├── components/               # 可复用组件
│   │   ├── common/               # 通用组件
│   │   ├── form/                 # 表单组件
│   │   ├── list/                 # 列表组件
│   │   ├── modal/                # 弹窗组件
│   │   ├── region/               # 地区选择组件
│   │   └── tabs/                 # 标签页组件
│   ├── constants/                # 常量定义
│   ├── hooks/                    # 自定义 Hooks
│   ├── navigation/               # 导航配置
│   │   ├── index.tsx             # 主导航配置
│   │   ├── rootNavigation.ts     # 根导航工具
│   │   └── types.ts              # 导航类型定义
│   ├── screens/                  # 页面组件
│   │   ├── auth/                 # 认证相关页面
│   │   ├── bidding/              # 招投标页面
│   │   ├── company/              # 企业相关页面
│   │   ├── detail/               # 详情页面
│   │   ├── follow/               # 关注页面
│   │   ├── graph/                # 图谱页面
│   │   ├── home/                 # 首页
│   │   ├── profile/              # 个人中心
│   │   └── search/               # 搜索页面
│   ├── services/                 # 业务服务
│   ├── stores/                   # 状态管理
│   │   └── userStore.ts          # 用户状态管理
│   ├── types/                    # 类型定义
│   └── utils/                    # 工具函数
│       ├── logger.ts             # 日志工具
│       └── storage.ts            # 存储工具
├── android/                      # Android 平台代码
├── ios/                          # iOS 平台代码
├── App.tsx                       # 应用入口
├── package.json                  # 项目依赖配置
├── tailwind.config.js            # Tailwind CSS 配置
├── tsconfig.json                 # TypeScript 配置
├── alova.config.ts               # Alova 配置
└── swagger.json                  # API 文档
```

## 核心模块详解

### 1. 导航系统 (Navigation)
应用采用多层级导航结构：
- **根导航**: 管理认证状态和主要页面切换
- **Tab 导航**: 底部标签栏，包含四个主要模块
- **Stack 导航**: 各模块内的页面栈管理
- **Modal 导航**: 弹窗和详情页面

### 2. 状态管理 (State Management)
使用 Zustand 进行状态管理：
- **用户状态**: 登录状态、用户信息、Token 管理
- **持久化**: 结合 MMKV 实现数据持久化存储
- **类型安全**: 完整的 TypeScript 类型定义

### 3. 网络请求 (API Layer)
基于 Alova 构建的网络层：
- **自动生成**: 基于 Swagger/OpenAPI 自动生成 API 接口
- **请求拦截**: 自动添加认证头和通用配置
- **响应处理**: 统一的错误处理和日志记录
- **缓存策略**: 智能缓存和状态同步

### 4. UI 组件系统
模块化的组件设计：
- **通用组件**: Button、Card、Header 等基础组件
- **业务组件**: SearchBar、FilterDropdown 等业务组件
- **表单组件**: Input、VerificationCodeInput 等表单组件
- **样式系统**: 基于 NativeWind 的 Tailwind CSS 样式

## 开发环境配置

### 环境要求
- Node.js >= 18
- React Native CLI
- Android Studio (Android 开发)
- Xcode (iOS 开发)

### 安装依赖
```bash
npm install
```

### iOS 依赖安装
```bash
cd ios && pod install
```

### 启动开发服务器
```bash
npm start
```

### 运行应用
```bash
# Android
npm run android

# iOS
npm run ios
```

### 代码生成
```bash
# 根据 Swagger 文档生成 API 接口
npm run gen
```

## 开发规范

### 代码规范
- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 和 Prettier 配置
- 组件采用函数式组件 + Hooks 模式
- 使用 StyleSheet.create 创建样式（优先使用别名路径）

### 文件命名
- 组件文件使用 PascalCase (如: `HomeScreen.tsx`)
- 工具文件使用 camelCase (如: `storage.ts`)
- 常量文件使用 camelCase (如: `filterOptions.ts`)

### 导入规范
- 使用别名路径 (`@/` 代替相对路径)
- 按类型分组导入 (React、第三方库、本地模块)
- 优先使用命名导入

## API 接口

### 基础配置
- **基础 URL**: `https://api.qike366.com`
- **认证方式**: Bearer Token
- **数据格式**: JSON

### 主要接口模块
- `/api/aicustomers`: 客户相关接口
- `/api/uac`: 用户中心接口
- `/api/scrm`: SCRM 相关接口
- `/api/bid`: 招投标接口 (qibiaoduo.com)

## 部署说明

### Android 打包
```bash
cd android
./gradlew assembleRelease
```

### iOS 打包
1. 在 Xcode 中打开 `ios/mof.xcworkspace`
2. 选择 Release 配置
3. Archive 并上传到 App Store Connect

## 测试

### 运行测试
```bash
npm test
```

### 测试覆盖率
```bash
npm test -- --coverage
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用私有许可证，仅供内部使用。

## 联系方式

如有问题或建议，请联系开发团队。