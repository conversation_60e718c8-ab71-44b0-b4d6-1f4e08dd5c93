{"extends": "@react-native/typescript-config", "compilerOptions": {"jsx": "react-native", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@screens/*": ["src/screens/*"], "@navigation/*": ["src/navigation/*"], "@assets/*": ["src/assets/*"], "@hooks/*": ["src/hooks/*"], "@utils/*": ["src/utils/*"], "@api/*": ["src/api/*"], "@constants/*": ["src/constants/*"], "@store/*": ["src/store/*"], "@types/*": ["src/types/*"]}}}