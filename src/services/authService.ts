import Apis from '../api';
import { UserInfo } from '../stores/userStore';
import { logger } from '../utils/logger';

// 登录请求参数
export interface LoginParams {
  phone: string;
  password?: string;
  code?: string;
  type: 'password' | 'code';
}

// 注册请求参数
export interface RegisterParams {
  phone: string;
  code: string;
  password: string;
  confirmPassword: string;
}

// 发送验证码参数
export interface SendCodeParams {
  phone: string;
  type: 'login' | 'register' | 'reset';
}

// API响应基础接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 登录响应数据
export interface LoginResponse {
  token: string;
  userInfo: UserInfo;
  expiresIn?: number;
}

// 验证码响应数据
export interface SendCodeResponse {
  success: boolean;
  message: string;
  expireTime?: number;
}

// 认证服务类
export class AuthService {
  /**
   * 密码登录
   */
  static async loginWithPassword(
    phone: string,
    password: string,
  ): Promise<LoginResponse> {
    try {
      logger.logAuth('Starting password login', { phone });

      // 这里需要根据实际的API接口调整
      // 从API定义中看到有oauth相关的接口，可能需要使用那些
      const response = await (Apis.general.get_api_uac_oauth_token as any)({
        params: {
          grant_type: 'password',
          username: phone,
          password: password,
        },
      });

      const result = await response;

      if ((result as any).success) {
        const loginData = {
          token: (result as any).data.access_token,
          userInfo: {
            id: (result as any).data.user_id || phone,
            phone: phone,
            nickname: (result as any).data.nickname,
            avatar: (result as any).data.avatar,
            email: (result as any).data.email,
          },
          expiresIn: (result as any).data.expires_in,
        };
        logger.logAuth('Password login successful', {
          phone,
          hasToken: !!loginData.token,
        });
        return loginData;
      } else {
        logger.logAuth('Password login failed', {
          phone,
          message: (result as any).message,
        });
        throw new Error((result as any).message || '登录失败');
      }
    } catch (error: any) {
      console.error('Password login error:', error);
      throw new Error(error.message || '登录失败，请检查网络连接');
    }
  }

  /**
   * 验证码登录
   */
  static async loginWithCode(
    phone: string,
    code: string,
  ): Promise<LoginResponse> {
    try {
      logger.logAuth('Starting code login', { phone });

      // 验证验证码
      const verifyResponse = await (
        Apis.general.post_api_uac_sms_valid_code_bid as any
      )({
        data: {
          phone: phone,
          code: code,
          type: 'login',
        },
      });

      const verifyResult = await verifyResponse;

      if (!(verifyResult as any).success) {
        throw new Error((verifyResult as any).message || '验证码错误');
      }

      // 获取token
      const tokenResponse = await (
        Apis.general.get_api_uac_tenants_access_token as any
      )({
        params: {
          phone: phone,
          code: code,
        },
      });

      const tokenResult = await tokenResponse;

      if (tokenResult.success) {
        return {
          token: tokenResult.data.access_token,
          userInfo: {
            id: tokenResult.data.user_id || phone,
            phone: phone,
            nickname: tokenResult.data.nickname,
            avatar: tokenResult.data.avatar,
            email: tokenResult.data.email,
          },
          expiresIn: tokenResult.data.expires_in,
        };
      } else {
        throw new Error(tokenResult.message || '登录失败');
      }
    } catch (error: any) {
      console.error('Code login error:', error);
      throw new Error(error.message || '登录失败，请检查网络连接');
    }
  }

  /**
   * 注册
   */
  static async register(params: RegisterParams): Promise<LoginResponse> {
    try {
      if (params.password !== params.confirmPassword) {
        throw new Error('两次输入的密码不一致');
      }

      // 先验证验证码
      const verifyResponse = await (
        Apis.general.post_api_uac_sms_valid_code_bid as any
      )({
        data: {
          phone: params.phone,
          code: params.code,
          type: 'register',
        },
      });

      const verifyResult = await verifyResponse;

      if (!verifyResult.success) {
        throw new Error(verifyResult.message || '验证码错误');
      }

      // 注册用户
      const registerResponse = await (
        Apis.general.post_api_uac_third_personal_register as any
      )({
        data: {
          phone: params.phone,
          password: params.password,
          code: params.code,
        },
      });

      const registerResult = await registerResponse;

      if (registerResult.success) {
        // 注册成功后自动登录
        return await this.loginWithPassword(params.phone, params.password);
      } else {
        throw new Error(registerResult.message || '注册失败');
      }
    } catch (error: any) {
      console.error('Register error:', error);
      throw new Error(error.message || '注册失败，请检查网络连接');
    }
  }

  /**
   * 发送验证码
   */
  static async sendCode(params: SendCodeParams): Promise<SendCodeResponse> {
    try {
      logger.logAuth('Sending verification code', {
        phone: params.phone,
        type: params.type,
      });

      let response;

      if (params.type === 'register') {
        // 注册验证码使用专门的接口
        response = await (Apis.general.post_api_sms_indetifycodebid as any)({
          data: {
            phoneNumber: params.phone,
          },
        });
      } else {
        // 登录和重置密码验证码使用通用接口
        response = await (Apis.general.post_api_uac_send_sms_bid_code as any)({
          data: {
            mobile: params.phone,
            type: params.type,
          },
        });
      }

      const result = await response;

      const isSuccess = result.code === '200';

      return {
        success: isSuccess,
        message:
          result.message || (isSuccess ? '验证码发送成功' : '验证码发送失败'),
      };
    } catch (error: any) {
      console.error('Send code error:', error);
      return {
        success: false,
        message: error.message || '验证码发送失败，请检查网络连接',
      };
    }
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(): Promise<UserInfo> {
    try {
      const response = await Apis.general.get_api_scrm_users_current();
      const result = await response;

      if (result.success) {
        return {
          id: result.data.id,
          phone: result.data.phone,
          nickname: result.data.nickname,
          avatar: result.data.avatar,
          email: result.data.email,
          createTime: result.data.createTime,
          updateTime: result.data.updateTime,
        };
      } else {
        throw new Error(result.message || '获取用户信息失败');
      }
    } catch (error: any) {
      console.error('Get current user error:', error);
      throw new Error(error.message || '获取用户信息失败');
    }
  }

  /**
   * 检查手机号是否已注册
   */
  static async checkPhoneRegistered(phone: string): Promise<boolean> {
    try {
      const response = await (
        Apis.general.post_api_uac_third_personal_register_hasreg as any
      )({
        data: { phone },
      });

      const result = await response;
      return result.data?.hasRegistered || false;
    } catch (error: any) {
      console.error('Check phone registered error:', error);
      return false;
    }
  }
}
