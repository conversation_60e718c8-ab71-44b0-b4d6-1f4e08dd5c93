import { create } from 'zustand';
import { Storage, StorageKeys } from '../utils/storage';

// 用户信息接口
export interface UserInfo {
  id: string;
  phone: string;
  nickname?: string;
  avatar?: string;
  email?: string;
  createTime?: string;
  updateTime?: string;
}

// 用户状态接口
interface UserState {
  // 状态
  isLoggedIn: boolean;
  userInfo: UserInfo | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;

  // 操作方法
  login: (userInfo: UserInfo, token: string) => void;
  logout: () => void;
  updateUserInfo: (userInfo: Partial<UserInfo>) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // 初始化方法
  initializeAuth: () => void;
}

// 创建用户状态store
export const useUserStore = create<UserState>((set, get) => ({
  // 初始状态
  isLoggedIn: false,
  userInfo: null,
  token: null,
  isLoading: false,
  error: null,

  // 登录
  login: (userInfo: UserInfo, token: string) => {
    // 更新状态
    set({
      isLoggedIn: true,
      userInfo,
      token,
      error: null,
    });

    // 持久化存储
    Storage.setBoolean(StorageKeys.IS_LOGGED_IN, true);
    Storage.setObject(StorageKeys.USER_INFO, userInfo);
    Storage.setString(StorageKeys.USER_TOKEN, token);
    Storage.setString(StorageKeys.LAST_LOGIN_PHONE, userInfo.phone);
  },

  // 登出
  logout: () => {
    // 清除状态
    set({
      isLoggedIn: false,
      userInfo: null,
      token: null,
      error: null,
    });

    // 清除持久化数据
    Storage.delete(StorageKeys.IS_LOGGED_IN);
    Storage.delete(StorageKeys.USER_INFO);
    Storage.delete(StorageKeys.USER_TOKEN);
    // 保留最后登录的手机号，方便下次登录
  },

  // 更新用户信息
  updateUserInfo: (newUserInfo: Partial<UserInfo>) => {
    const currentUserInfo = get().userInfo;
    if (currentUserInfo) {
      const updatedUserInfo = { ...currentUserInfo, ...newUserInfo };
      set({ userInfo: updatedUserInfo });
      Storage.setObject(StorageKeys.USER_INFO, updatedUserInfo);
    }
  },

  // 设置加载状态
  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  // 设置错误
  setError: (error: string | null) => {
    set({ error });
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },

  // 初始化认证状态（从本地存储恢复）
  initializeAuth: () => {
    try {
      const isLoggedIn = Storage.getBoolean(StorageKeys.IS_LOGGED_IN) || false;
      const userInfo = Storage.getObject<UserInfo>(StorageKeys.USER_INFO);
      const token = Storage.getString(StorageKeys.USER_TOKEN);

      if (isLoggedIn && userInfo && token) {
        set({
          isLoggedIn: true,
          userInfo,
          token,
        });
      } else {
        // 如果数据不完整，清除所有登录状态
        get().logout();
      }
    } catch (error) {
      console.error('Failed to initialize auth state:', error);
      get().logout();
    }
  },
}));

// 导出便捷的选择器hooks
export const useAuth = () => {
  const { isLoggedIn, userInfo, token, isLoading, error } = useUserStore();
  return { isLoggedIn, userInfo, token, isLoading, error };
};

export const useAuthActions = () => {
  const { login, logout, updateUserInfo, setLoading, setError, clearError, initializeAuth } = useUserStore();
  return { login, logout, updateUserInfo, setLoading, setError, clearError, initializeAuth };
};
