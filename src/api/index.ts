import { create<PERSON>lova } from 'alova';
import fetchAdapter from 'alova/fetch';
import reactHook from 'alova/react';
import { createApis, withConfigType } from './createApis';
import { Storage, StorageKeys } from '../utils/storage';
import { logger } from '../utils/logger';

export const alovaInstance = createAlova({
  baseURL: 'https://api.qike366.com', // 根据实际API地址调整
  statesHook: reactHook,
  requestAdapter: fetchAdapter(),
  beforeRequest: method => {
    // 添加认证token
    const token = Storage.getString(StorageKeys.USER_TOKEN);
    if (token) {
      method.config.headers = {
        ...method.config.headers,
        Authorization: `Bearer ${token}`,
      };
    }

    // 添加通用请求头
    method.config.headers = {
      ...method.config.headers,
      'Content-Type': 'application/json',
      Accept: 'application/json',
    };

    // 记录请求日志
    logger.logRequest({
      method: method.type,
      url: method.url,
      headers: method.config.headers,
      data: method.data,
      params: method.config.params as Record<string, any>,
      timestamp: new Date().toISOString(),
    });

    // 在 method 上添加开始时间用于计算请求耗时
    (method as any).startTime = Date.now();
  },
  responded: {
    onSuccess: async (response, method) => {
      const data = await response.json();
      const duration = (method as any).startTime
        ? Date.now() - (method as any).startTime
        : undefined;

      // 记录响应日志
      logger.logResponse({
        method: method.type,
        url: method.url,
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        data: data,
        timestamp: new Date().toISOString(),
        duration,
      });

      return data;
    },
    onError: (error, method) => {
      // 记录错误日志
      logger.logError({
        method: method.type,
        url: method.url,
        status: error.status,
        statusText: error.statusText,
        message: error.message,
        error: error,
        timestamp: new Date().toISOString(),
      });

      // 如果是401错误，可能需要重新登录
      if (error.status === 401) {
        logger.logWarning('Unauthorized access, clearing auth data');
        // 清除本地存储的认证信息
        Storage.delete(StorageKeys.USER_TOKEN);
        Storage.delete(StorageKeys.USER_INFO);
        Storage.setBoolean(StorageKeys.IS_LOGGED_IN, false);
      }
      throw error;
    },
  },
});

export const $$userConfigMap = withConfigType({});

const Apis = createApis(alovaInstance, $$userConfigMap);

export default Apis;

/*
 https://api.qibiaoduo.com
/api/bid 标多


https://api.qike366.com
/api/aicustomers 客多
/api/uac 用户中心
/api/scrm SCRM 
*/
