import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  Image,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { FollowStackParamList } from '../../navigation/types';

type Props = NativeStackScreenProps<FollowStackParamList, 'Follows'>;

// 企业标签类型
type TagType = '大型' | 'A股' | '500强' | '国家高新技术';

// 企业类型
interface Company {
  id: string;
  name: string;
  logo: any; // 图片资源
  logoBackground: string; // logo背景色
  tags: TagType[];
}

const FollowsScreen: React.FC<Props> = () => {
  // 模拟关注的企业数据
  const [companies, setCompanies] = useState<Company[]>([
    {
      id: '1',
      name: '启蒙方科技有限公司',
      logo: require('../../assets/h1.png'),
      logoBackground: '#4169E1',
      tags: ['大型', 'A股', '500强', '国家高新技术'],
    },
    {
      id: '2',
      name: '小米科技科技有限公司',
      logo: require('../../assets/h2.png'),
      logoBackground: '#FF6347',
      tags: ['大型', 'A股', '500强', '国家高新技术'],
    },
    {
      id: '3',
      name: '小米科技科技有限公司',
      logo: require('../../assets/h2.png'),
      logoBackground: '#FF6347',
      tags: ['大型', 'A股', '500强', '国家高新技术'],
    },
    {
      id: '4',
      name: '启蒙方科技有限公司',
      logo: require('../../assets/h1.png'),
      logoBackground: '#4169E1',
      tags: ['大型', 'A股', '500强', '国家高新技术'],
    },
    {
      id: '5',
      name: '启蒙方科技有限公司',
      logo: require('../../assets/h1.png'),
      logoBackground: '#4169E1',
      tags: ['大型', 'A股', '500强', '国家高新技术'],
    },
    {
      id: '6',
      name: '启蒙方科技有限公司',
      logo: require('../../assets/h1.png'),
      logoBackground: '#4169E1',
      tags: ['大型', 'A股', '500强', '国家高新技术'],
    },
  ]);

  // 取消关注
  const handleUnfollow = (id: string) => {
    setCompanies(companies.filter(company => company.id !== id));
  };

  // 渲染标签
  const renderTag = (tag: TagType) => {
    let bgColor = 'bg-orange-100';
    let textColor = 'text-orange-500';

    if (tag === 'A股') {
      bgColor = 'bg-blue-100';
      textColor = 'text-blue-500';
    } else if (tag === '500强') {
      bgColor = 'bg-green-100';
      textColor = 'text-green-500';
    } else if (tag === '国家高新技术') {
      bgColor = 'bg-blue-100';
      textColor = 'text-blue-500';
    }

    return (
      <View key={tag} className={`px-2 py-1 rounded-sm mr-2 ${bgColor}`}>
        <Text className={`text-xs ${textColor}`}>{tag}</Text>
      </View>
    );
  };

  // 渲染企业项
  const renderCompanyItem = ({
    item,
    index,
  }: {
    item: Company;
    index: number;
  }) => {
    // 特殊处理第三个项目（索引为2），添加取消关注按钮
    const isSpecialItem = index === 2;

    return (
      <View
        className={`bg-white mb-2 p-4 ${
          isSpecialItem ? 'flex-row items-center' : ''
        }`}
      >
        {isSpecialItem ? (
          <>
            <View className="flex-1">
              <Text className="text-base font-medium mb-2">{item.name}</Text>
              <View className="flex-row flex-wrap">
                {item.tags.map(tag => renderTag(tag))}
              </View>
            </View>
            <TouchableOpacity
              className="bg-red-400 py-4 px-5 ml-2"
              onPress={() => handleUnfollow(item.id)}
            >
              <Text className="text-white text-center">取消{'\n'}关注</Text>
            </TouchableOpacity>
          </>
        ) : (
          <View className="flex-row">
            <View
              className="w-16 h-16 rounded-md mr-4 items-center justify-center"
              style={{ backgroundColor: item.logoBackground }}
            >
              <Image
                source={item.logo}
                className="w-10 h-10"
                resizeMode="contain"
              />
            </View>
            <View className="flex-1">
              <Text className="text-base font-medium mb-2">{item.name}</Text>
              <View className="flex-row flex-wrap">
                {item.tags.map(tag => renderTag(tag))}
              </View>
            </View>
          </View>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* 蓝色背景标题 */}
      <View className="bg-blue-500 py-6 items-center">
        <Text className="text-white text-xl font-medium">关注</Text>
      </View>

      {/* 企业列表 */}
      <FlatList
        data={companies}
        renderItem={renderCompanyItem}
        keyExtractor={item => item.id}
        contentContainerStyle={{ padding: 16 }}
        ListEmptyComponent={
          <View className="items-center justify-center py-20">
            <Text className="text-gray-400">暂无关注内容</Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

export default FollowsScreen;
