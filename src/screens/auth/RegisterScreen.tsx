import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Alert,
  TextInput,
  StyleSheet,
  ImageBackground,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { AuthStackParamList } from '../../navigation/types';
import NavigationHeader from '../../components/common/NavigationHeader';
import Button from '../../components/common/Button';
import PhoneIcon from '../../assets/svg/phone.svg';
import { useAuth, useAuthActions } from '../../stores/userStore';
import { AuthService } from '../../services/authService';

type Props = NativeStackScreenProps<AuthStackParamList, 'Register'>;

const RegisterScreen: React.FC<Props> = ({ navigation }) => {
  const [phone, setPhone] = useState('');

  // 获取状态和操作方法
  const { isLoading, error } = useAuth();
  const { setLoading, setError, clearError } = useAuthActions();

  // 清除错误信息
  useEffect(() => {
    if (error) {
      Alert.alert('提示', error, [
        { text: '确定', onPress: () => clearError() },
      ]);
    }
  }, [error, clearError]);

  // 验证输入
  const validateInput = (): boolean => {
    if (!phone.trim()) {
      Alert.alert('提示', '请输入手机号');
      return false;
    }

    if (phone.length !== 11) {
      Alert.alert('提示', '请输入正确的手机号');
      return false;
    }

    return true;
  };

  const handleNext = async () => {
    if (!validateInput()) return;

    if (true) {
      navigation.navigate('VerificationCode', {
        phone,
        type: 'register',
      });
      return;
    }

    try {
      setLoading(true);
      clearError();

      // 检查手机号是否已注册
      const isRegistered = await AuthService.checkPhoneRegistered(phone);
      if (isRegistered) {
        Alert.alert('提示', '该手机号已注册，请直接登录', [
          { text: '取消' },
          {
            text: '去登录',
            onPress: () => navigation.navigate('Login', { initialTab: 'code' }),
          },
        ]);
        return;
      }

      // 发送验证码
      const result = await AuthService.sendCode({
        phone,
        type: 'register',
      });

      if (result && result.success) {
        // 跳转到验证码页面
        navigation.navigate('VerificationCode', {
          phone,
          type: 'register',
        });
      } else {
        // 显示错误信息
        const errorMessage = result?.message || '发送验证码失败，请重试';
        setError(errorMessage);
      }
    } catch (err: any) {
      setError(err.message || '发送验证码失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ImageBackground
      source={require('../../assets/login-bg.png')}
      style={styles.container}
      resizeMode="cover"
    >
      {/* 导航头 */}
      <NavigationHeader
        backgroundColor="transparent"
        borderBottomWidth={0}
        onBackPress={() => navigation.goBack()}
      />

      <View style={styles.content}>
        {/* 手机号输入框 */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>注册</Text>
          <View style={styles.inputWrapper}>
            <View style={styles.iconContainer}>
              <PhoneIcon width={18} height={18} fill="#9CA3AF" />
            </View>
            <TextInput
              placeholder="手机号"
              value={phone}
              onChangeText={setPhone}
              keyboardType="phone-pad"
              placeholderTextColor="#9CA3AF"
              style={styles.textInput}
            />
          </View>
        </View>

        {/* 下一步按钮 */}
        <Button
          title="下一步"
          onPress={handleNext}
          disabled={!phone.trim()}
          loading={isLoading}
          gradient={true}
        />
      </View>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 26,
  },
  inputContainer: {
    marginBottom: 117,
  },
  inputLabel: {
    fontSize: 22,
    fontWeight: 600,
    color: '#070D2F',
    marginBottom: 60,
  },
  inputWrapper: {
    backgroundColor: '#ffffff',
    borderRadius: 30,
    paddingHorizontal: 24,
    height: 54,
    shadowRadius: 6,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    marginRight: 12,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#374151',
  },

  titleStyle: {
    fontSize: 28,
    fontWeight: '600',
    color: '#1f2937',
  },
});

export default RegisterScreen;
