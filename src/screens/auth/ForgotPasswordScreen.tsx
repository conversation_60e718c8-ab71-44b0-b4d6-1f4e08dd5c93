import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  Image,
  ImageBackground,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { AuthStackParamList } from '../../navigation/types';
import Input from '../../components/form/Input';

type Props = NativeStackScreenProps<AuthStackParamList, 'ForgotPassword'>;

const ForgotPasswordScreen: React.FC<Props> = ({ navigation }) => {
  const [phone, setPhone] = useState('');

  const handleSubmit = () => {
    // 验证手机号
    if (!phone.trim()) {
      return;
    }

    navigation.navigate('VerificationCode', { phone, type: 'reset' });
  };

  return (
    <ImageBackground
      source={require('../../assets/login-bg.png')}
      className="flex-1"
      resizeMode="cover"
    >
      <SafeAreaView className="flex-1">
        <View className="p-4">
          {/* 返回按钮 */}
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="mb-6"
          >
            <Text className="text-2xl text-gray-700">←</Text>
          </TouchableOpacity>

          {/* 标题 */}
          <Text className="text-2xl font-bold mb-8">找回密码</Text>

          {/* 手机号输入框 */}
          <View className="w-full bg-white rounded-full mb-8 flex-row items-center px-4 py-3">
            <Image
              source={require('../../assets/logo.png')}
              className="w-6 h-6 mr-2"
              resizeMode="contain"
            />
            <Input
              placeholder="手机号"
              value={phone}
              onChangeText={setPhone}
              keyboardType="phone-pad"
              containerStyle={{ flex: 1, marginBottom: 0 }}
              inputContainerStyle={{ borderWidth: 0 }}
            />
          </View>

          {/* 下一步按钮 */}
          <TouchableOpacity
            onPress={handleSubmit}
            className="w-full bg-blue-500 rounded-full py-4 items-center mb-4"
            disabled={!phone.trim()}
          >
            <Text className="text-white font-medium text-lg">下一步</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default ForgotPasswordScreen;
