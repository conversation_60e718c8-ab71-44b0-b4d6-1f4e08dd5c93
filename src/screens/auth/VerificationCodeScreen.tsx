import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ImageBackground,
  Alert,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { AuthStackParamList } from '../../navigation/types';
import NavigationHeader from '../../components/common/NavigationHeader';
import VerificationCodeInput from '../../components/form/VerificationCodeInput';
import * as RootNavigation from '../../navigation/rootNavigation';
import { useAuth, useAuthActions } from '../../stores/userStore';
import { AuthService } from '../../services/authService';

type Props = NativeStackScreenProps<AuthStackParamList, 'VerificationCode'>;

const VerificationCodeScreen: React.FC<Props> = ({ navigation, route }) => {
  console.log('验证码页面加载', route.params);

  // 确保route.params存在且包含phone属性
  const phone = route.params?.phone || '';
  const type = route.params?.type || 'login';
  const [code, setCode] = useState('');
  const [countdown, setCountdown] = useState(60);
  const [isResending, setIsResending] = useState(true); // 默认开始倒计时
  const [isVerifying, setIsVerifying] = useState(false);

  // 获取状态和操作方法
  const { isLoading, error } = useAuth();
  const { login, setLoading, setError, clearError } = useAuthActions();

  useEffect(() => {
    console.log('验证码页面挂载，手机号:', phone);

    // 初始化时自动开始倒计时
    const timer = setInterval(() => {
      if (countdown > 0) {
        setCountdown(countdown - 1);
      } else {
        setIsResending(false);
        clearInterval(timer);
      }
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, [countdown, phone]);

  // 清除错误信息
  useEffect(() => {
    if (error) {
      Alert.alert('提示', error, [
        { text: '确定', onPress: () => clearError() },
      ]);
    }
  }, [error, clearError]);

  const handleResendCode = async () => {
    if (!isResending) {
      try {
        setLoading(true);
        clearError();

        const result = await AuthService.sendCode({
          phone,
          type,
        });

        if (result && result.success) {
          setCountdown(60);
          setIsResending(true);
          Alert.alert('提示', '验证码已重新发送');
        } else {
          // 显示错误信息
          const errorMessage = result?.message || '发送验证码失败，请重试';
          setError(errorMessage);
        }
      } catch (error: any) {
        setError(error.message || '发送验证码失败');
      } finally {
        setLoading(false);
      }
    }
  };

  const handleCodeFilled = async (inputCode: string) => {
    console.log('验证码输入完成:', inputCode);
    setCode(inputCode);

    if (isVerifying) return; // 防止重复提交

    try {
      setIsVerifying(true);
      clearError();

      if (type === 'login') {
        // 验证码登录
        const result = await AuthService.loginWithCode(phone, inputCode);
        login(result.userInfo, result.token);
        RootNavigation.resetToMain();
      } else if (type === 'register') {
        // 注册流程，跳转到设置密码页面
        navigation.navigate('ResetPassword', {
          phone,
          code: inputCode,
          type: 'register',
        });
      } else if (type === 'reset') {
        // 重置密码流程
        navigation.navigate('ResetPassword', {
          phone,
          code: inputCode,
          type: 'reset',
        });
      }
    } catch (error: any) {
      setError(error.message || '验证失败');
      // 清空验证码输入
      setCode('');
    } finally {
      setIsVerifying(false);
    }
  };

  // 格式化手机号，如185xxxx8876
  const formatPhone = (phoneNumber: string) => {
    if (!phoneNumber || phoneNumber.length !== 11) return phoneNumber;
    return `${phoneNumber.substring(0, 3)}xxxx${phoneNumber.substring(7)}`;
  };

  return (
    <ImageBackground
      source={require('../../assets/login-bg.png')}
      style={styles.container}
      resizeMode="cover"
    >
      {/* 导航头 */}
      <NavigationHeader
        onBackPress={() => navigation.goBack()}
        backgroundColor="transparent"
      />

      <SafeAreaView style={styles.safeArea}>
        <View style={styles.content}>
          <Text style={styles.title}>输入验证码</Text>
          {/* 副标题 */}
          <Text style={styles.subtitle}>
            验证码已发送至 {formatPhone(phone)}
          </Text>

          {/* 验证码输入框 */}
          <View style={styles.inputContainer}>
            <VerificationCodeInput
              codeLength={6}
              onCodeFilled={handleCodeFilled}
              autoFocus={true}
              disabled={isVerifying}
            />
            {isVerifying && (
              <View style={styles.verifyingContainer}>
                <ActivityIndicator color="#3B82F6" />
                <Text style={styles.verifyingText}>验证中...</Text>
              </View>
            )}
          </View>

          {/* 倒计时/重新获取 */}
          <View style={styles.resendContainer}>
            {isResending ? (
              <Text style={styles.countdownText}>{countdown}秒后重新获取</Text>
            ) : (
              <TouchableOpacity onPress={handleResendCode} disabled={isLoading}>
                {isLoading ? (
                  <ActivityIndicator color="#3B82F6" size="small" />
                ) : (
                  <Text style={styles.resendText}>重新获取验证码</Text>
                )}
              </TouchableOpacity>
            )}
          </View>
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 16,
    marginTop: 26,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    lineHeight: 26,
    marginBottom: 12,
    color: '#000000',
  },
  subtitle: {
    color: '#6B7280',
    marginBottom: 36,
  },
  inputContainer: {
    marginBottom: 20,
  },
  verifyingContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
  },
  verifyingText: {
    color: '##C8294',
    marginLeft: 8,
    fontSize: 15,
  },
  resendContainer: {
    flexDirection: 'row',
    marginBottom: 32,
  },
  countdownText: {
    color: '#7C8294',
    fontSize: 15,
  },
  resendText: {
    color: '#4B74FE',
    fontSize: 15,
  },
});

export default VerificationCodeScreen;
