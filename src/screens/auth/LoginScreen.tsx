import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  Image,
  ImageBackground,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { AuthStackParamList } from '../../navigation/types';
import Input from '../../components/form/Input';
import * as RootNavigation from '../../navigation/rootNavigation';
import { useAuth, useAuthActions } from '../../stores/userStore';
import { AuthService } from '../../services/authService';
import { Storage, StorageKeys } from '../../utils/storage';

type Props = NativeStackScreenProps<AuthStackParamList, 'Login'>;

const LoginScreen: React.FC<Props> = ({ navigation, route }) => {
  const initialTab = route.params?.initialTab || 'code';
  const [loginType, setLoginType] = useState<'code' | 'password'>(initialTab);
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [agreed, setAgreed] = useState(false);

  // 获取状态和操作方法
  const { isLoading, error } = useAuth();
  const { login, setLoading, setError, clearError } = useAuthActions();

  // 当路由参数变化时更新登录类型
  useEffect(() => {
    if (route.params?.initialTab) {
      setLoginType(route.params.initialTab);
    }
  }, [route.params]);

  // 初始化时加载上次登录的手机号
  useEffect(() => {
    const lastPhone = Storage.getString(StorageKeys.LAST_LOGIN_PHONE);
    if (lastPhone) {
      setPhone(lastPhone);
    }
  }, []);

  // 清除错误信息
  useEffect(() => {
    if (error) {
      Alert.alert('提示', error, [
        { text: '确定', onPress: () => clearError() },
      ]);
    }
  }, [error, clearError]);

  // 验证输入
  const validateInput = (): boolean => {
    if (!agreed) {
      Alert.alert('提示', '请先同意用户协议');
      return false;
    }

    if (!phone.trim()) {
      Alert.alert('提示', '请输入手机号');
      return false;
    }

    if (phone.length !== 11) {
      Alert.alert('提示', '请输入正确的手机号');
      return false;
    }

    if (loginType === 'password' && !password.trim()) {
      Alert.alert('提示', '请输入密码');
      return false;
    }

    return true;
  };

  // 密码登录
  const handlePasswordLogin = async () => {
    if (!validateInput()) return;

    try {
      setLoading(true);
      clearError();

      const result = await AuthService.loginWithPassword(phone, password);

      // 登录成功，更新状态
      login(result.userInfo, result.token);

      // 跳转到主页
      RootNavigation.resetToMain();
    } catch (error: any) {
      setError(error.message || '登录失败');
    } finally {
      setLoading(false);
    }
  };

  // 发送验证码
  const handleSendCode = async () => {
    if (!validateInput()) return;

    try {
      setLoading(true);
      clearError();

      const result = await AuthService.sendCode({
        phone,
        type: 'login',
      });

      if (result && result.success) {
        // 跳转到验证码页面
        navigation.navigate('VerificationCode', {
          phone,
          type: 'login',
        });
      } else {
        // 显示错误信息
        const errorMessage = result?.message || '发送验证码失败，请重试';
        setError(errorMessage);
      }
    } catch (error: any) {
      setError(error.message || '发送验证码失败');
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = () => {
    if (loginType === 'code') {
      handleSendCode();
    } else {
      handlePasswordLogin();
    }
  };

  return (
    <ImageBackground
      source={require('../../assets/login-bg.png')}
      className="flex-1"
      resizeMode="cover"
    >
      <SafeAreaView className="flex-1">
        <View className="p-4 flex-1">
          {/* 返回按钮 */}
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="mb-4"
          >
            <Text className="text-gray-500 text-xl">←</Text>
          </TouchableOpacity>

          {/* Logo */}
          <View className="items-center mb-6">
            <Image
              source={require('../../assets/logo.png')}
              className="w-24 h-24"
              resizeMode="contain"
            />
          </View>

          {/* 登录方式切换 */}
          <View className="flex-row justify-center mb-6">
            <TouchableOpacity
              onPress={() => setLoginType('code')}
              className={`px-4 pb-2 ${
                loginType === 'code' ? 'border-b-2 border-blue-500' : ''
              }`}
            >
              <Text
                className={
                  loginType === 'code' ? 'text-blue-500' : 'text-gray-500'
                }
              >
                验证码登录
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => setLoginType('password')}
              className={`px-4 pb-2 ${
                loginType === 'password' ? 'border-b-2 border-blue-500' : ''
              }`}
            >
              <Text
                className={
                  loginType === 'password' ? 'text-blue-500' : 'text-gray-500'
                }
              >
                密码登录
              </Text>
            </TouchableOpacity>
          </View>

          {loginType === 'code' ? (
            // 验证码登录
            <View>
              <View className="mb-6">
                <Input
                  placeholder="手机号"
                  value={phone}
                  onChangeText={setPhone}
                  keyboardType="phone-pad"
                />
              </View>
            </View>
          ) : (
            // 密码登录
            <View>
              <View className="mb-4">
                <Input
                  placeholder="手机号"
                  value={phone}
                  onChangeText={setPhone}
                  keyboardType="phone-pad"
                />
              </View>

              <View className="mb-6 flex-row">
                <View className="flex-1">
                  <Input
                    placeholder="密码"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry
                  />
                </View>
                <TouchableOpacity
                  className="ml-2 self-center"
                  onPress={() => navigation.navigate('ForgotPassword')}
                >
                  <Text className="text-blue-500">找回密码</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}

          {/* 登录按钮 */}
          <TouchableOpacity
            onPress={handleLogin}
            disabled={isLoading}
            className={`rounded-full py-3 items-center mb-4 ${
              isLoading ? 'bg-gray-400' : 'bg-blue-500'
            }`}
          >
            {isLoading ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text className="text-white font-medium text-lg">
                {loginType === 'code' ? '获取验证码' : '登录'}
              </Text>
            )}
          </TouchableOpacity>

          {/* 注册按钮 */}
          <TouchableOpacity
            onPress={() => navigation.navigate('Register')}
            className="items-center mb-4"
          >
            <Text className="text-blue-500">注册</Text>
          </TouchableOpacity>

          {/* 用户协议 */}
          <View className="flex-row items-center justify-center mt-4">
            <TouchableOpacity
              onPress={() => setAgreed(!agreed)}
              className="w-5 h-5 rounded-full mr-2 items-center justify-center"
              style={{ borderWidth: 1, borderColor: '#3B82F6' }}
            >
              {agreed && <View className="w-3 h-3 bg-blue-500 rounded-full" />}
            </TouchableOpacity>
            <Text className="text-gray-500 text-xs">
              已阅读并同意 <Text className="text-blue-500">《用户协议》</Text>{' '}
              和 <Text className="text-blue-500">《免责声明》</Text>
            </Text>
          </View>

          {/* 底部指示条 */}
          <View className="flex-1 justify-end items-center pb-4">
            <View className="w-32 h-1 bg-black rounded-full" />
          </View>
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default LoginScreen;
