import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  SafeAreaView,
  ImageBackground,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { AuthStackParamList } from '../../navigation/types';
import * as RootNavigation from '../../navigation/rootNavigation';
import Button from '../../components/common/Button';
import WarmTip from '../../components/common/WarmTip';

type Props = NativeStackScreenProps<AuthStackParamList, 'OneClickLogin'>;

const OneClickLoginScreen: React.FC<Props> = ({ navigation }) => {
  const [agreed, setAgreed] = useState(false); // 默认未勾选
  const [phone, _setPhone] = useState('152****3351');
  const [showWarmTip, setShowWarmTip] = useState(false);

  const handleLogin = () => {
    if (!agreed) {
      // 显示温馨提示
      setShowWarmTip(true);
      return;
    }

    // 跳转到验证码页面
    navigation.navigate('VerificationCode', {
      phone: phone,
      type: 'login',
    });
  };

  const handleWarmTipConfirm = () => {
    setShowWarmTip(false);
  };

  return (
    <ImageBackground
      source={require('../../assets/login-bg.png')}
      className="flex-1"
      resizeMode="cover"
    >
      <SafeAreaView className="flex-1">
        <View className="flex-1 px-8 pt-20 pb-8 justify-between">
          {/* 中间内容区域 */}
          <View className="items-center">
            {/* Logo */}
            <View className="mb-8">
              <Image
                source={require('../../assets/logo.png')}
                className="w-24 h-24"
                resizeMode="contain"
              />
            </View>

            {/* 手机号显示 */}
            <Text className="text-2xl font-bold text-gray-800 mb-2">
              {phone}
            </Text>

            {/* 中国联通认证 */}
            <Text className="text-gray-500 mb-8">中国联通认证</Text>

            {/* 一键登录按钮 */}
            <Button
              title="一键登录"
              onPress={handleLogin}
              type="primary"
              size="large"
              gradient={true}
              gradientColors={
                agreed ? ['#819EFF', '#4B74FF'] : ['#9CA3AF', '#9CA3AF']
              }
              style={{ marginBottom: 24 }}
            />

            {/* 用户协议 */}
            <View className="flex-row items-center justify-center mb-6">
              <TouchableOpacity
                onPress={() => setAgreed(!agreed)}
                className="w-6 h-6 rounded-full mr-2 items-center justify-center"
              >
                {agreed && (
                  <View className="w-6 h-6 bg-blue-500 rounded-full items-center justify-center">
                    <Text className="text-white text-xs">✓</Text>
                  </View>
                )}
                {!agreed && (
                  <View className="w-6 h-6 border border-gray-300 rounded-full" />
                )}
              </TouchableOpacity>
              <Text className="text-gray-500 text-xs flex-shrink">
                已阅读并同意{' '}
                <Text className="text-blue-500">《中国联通认证服务条款》</Text>{' '}
                以及
                <Text className="text-blue-500">
                  《启魔方用户协议》
                </Text> 和{' '}
                <Text className="text-blue-500">《启魔方免责声明》</Text>
              </Text>
            </View>
          </View>

          {/* 底部区域 */}
          <View className="items-center">
            {/* 注册按钮 */}
            <TouchableOpacity
              onPress={() => navigation.navigate('Register')}
              className="items-center mb-4"
            >
              <Text className="text-blue-500">注册</Text>
            </TouchableOpacity>

            {/* 其他登录方式 */}
            <TouchableOpacity
              onPress={() =>
                navigation.navigate('Login', { initialTab: 'code' })
              }
              className="items-center"
            >
              <Text className="text-blue-500">其他登录方式</Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>

      {/* 温馨提示 */}
      <WarmTip
        visible={showWarmTip}
        onClose={() => setShowWarmTip(false)}
        title="温馨提示"
        content="已阅读并同意《中国联通认证服务条款》以及《启魔方用户协议》和《启魔方免责声明》"
        confirmText="我知道了"
        onConfirm={handleWarmTipConfirm}
      />
    </ImageBackground>
  );
};

export default OneClickLoginScreen;
