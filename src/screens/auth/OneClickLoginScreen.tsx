import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  SafeAreaView,
  ImageBackground,
  StyleSheet,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { AuthStackParamList } from '../../navigation/types';
import Button from '../../components/common/Button';
import WarmTip from '../../components/common/WarmTip';

type Props = NativeStackScreenProps<AuthStackParamList, 'OneClickLogin'>;

const OneClickLoginScreen: React.FC<Props> = ({ navigation }) => {
  const [agreed, setAgreed] = useState(false); // 默认未勾选
  const [phone, _setPhone] = useState('152****3351');
  const [showWarmTip, setShowWarmTip] = useState(false);

  const handleLogin = () => {
    if (!agreed) {
      // 显示温馨提示
      setShowWarmTip(true);
      return;
    }

    // 跳转到验证码页面
    navigation.navigate('VerificationCode', {
      phone: phone,
      type: 'login',
    });
  };

  const handleWarmTipConfirm = () => {
    setShowWarmTip(false);
  };

  return (
    <ImageBackground
      source={require('../../assets/login-bg.png')}
      style={styles.backgroundImage}
      resizeMode="cover"
    >
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.container}>
          {/* 中间内容区域 */}
          <View style={styles.centerContent}>
            {/* Logo */}
            <View style={styles.logoContainer}>
              <Image
                source={require('../../assets/logo.png')}
                style={styles.logo}
                resizeMode="contain"
              />
            </View>

            {/* 手机号显示 */}
            <Text style={styles.phoneText}>{phone}</Text>

            {/* 中国联通认证 */}
            <Text style={styles.authText}>中国联通认证</Text>

            {/* 一键登录按钮 */}
            <Button
              title="一键登录"
              onPress={handleLogin}
              type="primary"
              size="large"
              gradient={true}
              gradientColors={
                agreed ? ['#819EFF', '#4B74FF'] : ['#9CA3AF', '#9CA3AF']
              }
              style={styles.loginButton}
            />

            {/* 用户协议 */}
            <View style={styles.agreementContainer}>
              <TouchableOpacity
                onPress={() => setAgreed(!agreed)}
                style={styles.checkboxContainer}
              >
                {agreed && (
                  <View style={styles.checkboxChecked}>
                    <Text style={styles.checkboxText}>✓</Text>
                  </View>
                )}
                {!agreed && <View style={styles.checkboxUnchecked} />}
              </TouchableOpacity>
              <Text style={styles.agreementText}>
                已阅读并同意{' '}
                <Text style={styles.linkText}>《中国联通认证服务条款》</Text>{' '}
                以及
                <Text style={styles.linkText}>《启魔方用户协议》</Text> 和{' '}
                <Text style={styles.linkText}>《启魔方免责声明》</Text>
              </Text>
            </View>
          </View>

          {/* 底部区域 */}
          <View style={styles.bottomArea}>
            {/* 注册按钮 */}
            <TouchableOpacity
              onPress={() => navigation.navigate('Register')}
              style={styles.registerButton}
            >
              <Text style={styles.linkText}>注册</Text>
            </TouchableOpacity>

            {/* 其他登录方式 */}
            <TouchableOpacity
              onPress={() =>
                navigation.navigate('Login', { initialTab: 'code' })
              }
              style={styles.otherLoginButton}
            >
              <Text style={styles.linkText}>其他登录方式</Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>

      {/* 温馨提示 */}
      <WarmTip
        visible={showWarmTip}
        onClose={() => setShowWarmTip(false)}
        title="温馨提示"
        content="已阅读并同意《中国联通认证服务条款》以及《启魔方用户协议》和《启魔方免责声明》"
        confirmText="我知道了"
        onConfirm={handleWarmTipConfirm}
      />
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingHorizontal: 32,
    paddingTop: 130,
    paddingBottom: 32,
    justifyContent: 'space-between',
  },
  centerContent: {
    alignItems: 'center',
  },
  logoContainer: {
    marginBottom: 42,
  },
  logo: {
    width: 76,
    height: 76,
  },
  phoneText: {
    fontSize: 26,
    fontWeight: 'bold',
    color: '#070D2F',
    marginBottom: 16,
  },
  authText: {
    color: '#7C8294',
    marginBottom: 32,
    fontSize: 12,
  },
  loginButton: {
    marginBottom: 24,
  },
  agreementContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  checkboxContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxChecked: {
    width: 24,
    height: 24,
    backgroundColor: '#3B82F6',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxUnchecked: {
    width: 24,
    height: 24,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 12,
  },
  checkboxText: {
    color: 'white',
    fontSize: 12,
  },
  agreementText: {
    color: '#6B7280',
    fontSize: 12,
    flex: 1,
  },
  linkText: {
    color: '#3B82F6',
  },
  bottomArea: {
    alignItems: 'center',
  },
  registerButton: {
    alignItems: 'center',
    marginBottom: 16,
  },
  otherLoginButton: {
    alignItems: 'center',
  },
});

export default OneClickLoginScreen;
