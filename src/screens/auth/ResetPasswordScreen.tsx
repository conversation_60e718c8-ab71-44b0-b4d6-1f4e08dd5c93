import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ImageBackground,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { AuthStackParamList } from '../../navigation/types';
import Input from '../../components/form/Input';
import { useAuth, useAuthActions } from '../../stores/userStore';
import { AuthService } from '../../services/authService';
import * as RootNavigation from '../../navigation/rootNavigation';

type Props = NativeStackScreenProps<AuthStackParamList, 'ResetPassword'>;

const ResetPasswordScreen: React.FC<Props> = ({ navigation, route }) => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // 从路由参数获取信息
  const phone = route.params?.phone || '';
  const code = route.params?.code || '';
  const type = route.params?.type || 'reset'; // 'register' | 'reset'

  // 获取状态和操作方法
  const { isLoading, error } = useAuth();
  const { login, setLoading, setError, clearError } = useAuthActions();

  // 清除错误信息
  useEffect(() => {
    if (error) {
      Alert.alert('提示', error, [
        { text: '确定', onPress: () => clearError() },
      ]);
    }
  }, [error, clearError]);

  // 验证密码
  const validatePassword = (): boolean => {
    if (!password.trim()) {
      Alert.alert('提示', '请输入密码');
      return false;
    }

    if (password.length < 6 || password.length > 20) {
      Alert.alert('提示', '密码长度应为6-20位');
      return false;
    }

    if (!confirmPassword.trim()) {
      Alert.alert('提示', '请确认密码');
      return false;
    }

    if (password !== confirmPassword) {
      Alert.alert('提示', '两次输入的密码不一致');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validatePassword()) return;

    try {
      setLoading(true);
      clearError();

      if (type === 'register') {
        // 注册流程
        const result = await AuthService.register({
          phone,
          code,
          password,
          confirmPassword,
        });

        // 注册成功，自动登录
        login(result.userInfo, result.token);
        RootNavigation.resetToMain();
      } else {
        // 重置密码流程 - 这里需要实现重置密码的API
        // 暂时跳转到登录页面
        Alert.alert('提示', '密码重置成功', [
          {
            text: '确定',
            onPress: () =>
              navigation.navigate('Login', { initialTab: 'password' }),
          },
        ]);
      }
    } catch (error: any) {
      setError(
        error.message || (type === 'register' ? '注册失败' : '重置密码失败'),
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <ImageBackground
      source={require('../../assets/login-bg.png')}
      className="flex-1"
      resizeMode="cover"
    >
      <SafeAreaView className="flex-1">
        <View className="p-4">
          {/* 返回按钮 */}
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="mb-6"
          >
            <Text className="text-2xl text-gray-700">←</Text>
          </TouchableOpacity>

          {/* 标题 */}
          <Text className="text-2xl font-bold mb-2">
            {type === 'register' ? '设置密码' : '设置新密码'}
          </Text>
          <Text className="text-gray-500 mb-8">
            请输入{type === 'register' ? '' : '新'}密码 (6-20位)
          </Text>

          {/* 密码输入框 */}
          <View className="mb-4">
            <Input
              placeholder="输入密码"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
            />
          </View>

          {/* 确认密码输入框 */}
          <View className="mb-8">
            <Input
              placeholder="确认密码"
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry
            />
          </View>

          {/* 提交按钮 */}
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={isLoading || !password || !confirmPassword}
            className={`w-full rounded-full py-4 items-center mb-4 ${
              isLoading || !password || !confirmPassword
                ? 'bg-gray-400'
                : 'bg-blue-500'
            }`}
          >
            {isLoading ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text className="text-white font-medium text-lg">
                {type === 'register' ? '完成注册' : '提交'}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default ResetPasswordScreen;
