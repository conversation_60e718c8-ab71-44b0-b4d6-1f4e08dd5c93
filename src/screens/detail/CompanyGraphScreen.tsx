import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  StyleSheet,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { DetailStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';

type Props = NativeStackScreenProps<DetailStackParamList, 'CompanyGraph'>;

// 定义标签类型
type TabType = '企业图谱' | '关系图谱' | '股权穿透图' | '企业合作关系图';

// 图谱数据类型 - 节点
interface GraphNodeType {
  id: string;
  name: string;
  position?: string;
  type: 'company' | 'person';
  percent?: string;
}

// 图谱数据类型 - 关系
interface GraphRelationshipType {
  source: string;
  target: string;
  type: string; // 法定代表人、实际控制人、最终受益人、股东、高管、历史股东、历史高管
  percent?: string;
}

const CompanyGraphScreen: React.FC<Props> = ({ navigation, route }) => {
  const { id } = route.params;
  const [activeTab, setActiveTab] = useState<TabType>('企业图谱');

  // 模拟企业信息
  const companyInfo = {
    id,
    name: '科技有限责任公司',
  };

  // 模拟图谱数据
  const graphData: {
    nodes: GraphNodeType[];
    relationships: GraphRelationshipType[];
  } = {
    nodes: [
      { id: 'main', name: companyInfo.name, type: 'company' },
      { id: 'legal', name: '法定代表人', type: 'person' },
      { id: 'actual', name: '实际控制人', type: 'person' },
      { id: 'benefit', name: '最终受益人', type: 'person' },
      { id: 'shareholder', name: '股东', type: 'person' },
      { id: 'executive', name: '高管', type: 'person' },
      { id: 'historical_shareholder', name: '历史股东', type: 'person' },
      { id: 'historical_executive', name: '历史高管', type: 'person' },
      { id: 'person1', name: '雷军', type: 'person' },
      { id: 'person2', name: '雷军', type: 'person', percent: '77.987%' },
      { id: 'person3', name: '雷军', type: 'person', percent: '77.987%' },
      { id: 'person4', name: '雷军', type: 'person', percent: '77.987%' },
      { id: 'person5', name: '雷军', type: 'person', percent: '77.987%' },
      { id: 'person6', name: '雷军', type: 'person', percent: '77.987%' },
      { id: 'person7', name: '雷军', type: 'person', percent: '77.987%' },
    ],
    relationships: [
      { source: 'main', target: 'legal', type: '法定代表人' },
      { source: 'main', target: 'actual', type: '实际控制人' },
      { source: 'main', target: 'benefit', type: '最终受益人' },
      { source: 'main', target: 'shareholder', type: '股东' },
      { source: 'main', target: 'executive', type: '高管' },
      { source: 'main', target: 'historical_shareholder', type: '历史股东' },
      { source: 'main', target: 'historical_executive', type: '历史高管' },
      { source: 'legal', target: 'person1', type: '法定代表人' },
      {
        source: 'actual',
        target: 'person2',
        type: '实际控制人',
        percent: '77.987%',
      },
      {
        source: 'benefit',
        target: 'person3',
        type: '最终受益人',
        percent: '77.987%',
      },
      {
        source: 'shareholder',
        target: 'person4',
        type: '股东',
        percent: '77.987%',
      },
      {
        source: 'executive',
        target: 'person5',
        type: '高管',
        percent: '77.987%',
      },
      {
        source: 'historical_shareholder',
        target: 'person6',
        type: '历史股东',
        percent: '77.987%',
      },
      {
        source: 'historical_executive',
        target: 'person7',
        type: '历史高管',
        percent: '77.987%',
      },
    ],
  };

  // 渲染图谱关系
  const renderGraphRelationships = () => {
    const relationshipTypes = [
      { id: 'legal', name: '法定代表人', color: '#FED8B1' }, // 橙色系
      { id: 'actual', name: '实际控制人', color: '#D1F0C2' }, // 绿色系
      { id: 'benefit', name: '最终受益人', color: '#FFCCCB' }, // 红色系
      { id: 'shareholder', name: '股东', color: '#E6E6FA' }, // 紫色系
      { id: 'executive', name: '高管', color: '#E6E6FA' }, // 紫色系
      { id: 'historical_shareholder', name: '历史股东', color: '#E6E6FA' }, // 紫色系
      { id: 'historical_executive', name: '历史高管', color: '#E6E6FA' }, // 紫色系
    ];

    return (
      <View style={styles.graphContainer}>
        <View style={styles.mainCompanyContainer}>
          <View style={styles.mainCompanyBox}>
            <Text style={styles.mainCompanyText}>{companyInfo.name}</Text>
          </View>
        </View>

        <View style={styles.relationshipsContainer}>
          {relationshipTypes.map(type => {
            const relationships = graphData.relationships.filter(
              r => r.type === type.name,
            );
            if (relationships.length === 0) return null;

            return (
              <View key={type.id} style={styles.relationshipRow}>
                <View
                  style={[
                    styles.relationshipType,
                    { backgroundColor: type.color },
                  ]}
                >
                  <Text style={styles.relationshipTypeText}>{type.name}</Text>
                </View>
                <View style={styles.relationshipLine}>
                  <Text style={styles.lineDash}>- - - -</Text>
                </View>
                <View style={styles.relationshipTargets}>
                  {relationships.map((rel, index) => {
                    const targetNode = graphData.nodes.find(
                      n => n.id === rel.target,
                    );
                    if (
                      !targetNode ||
                      targetNode.id === 'legal' ||
                      targetNode.id === 'actual' ||
                      targetNode.id === 'benefit' ||
                      targetNode.id === 'shareholder' ||
                      targetNode.id === 'executive' ||
                      targetNode.id === 'historical_shareholder' ||
                      targetNode.id === 'historical_executive'
                    )
                      return null;

                    return (
                      <View key={index} style={styles.targetNodeContainer}>
                        <View style={styles.targetNode}>
                          <Text style={styles.targetNodeText}>
                            {targetNode.name}
                          </Text>
                        </View>
                        {targetNode.percent && (
                          <Text style={styles.percentText}>
                            ({targetNode.percent})
                          </Text>
                        )}
                      </View>
                    );
                  })}
                </View>
              </View>
            );
          })}
        </View>

        <TouchableOpacity
          style={styles.checkDetailButton}
          onPress={() => navigation.navigate('CompanyDetail', { id })}
        >
          <Text style={styles.checkDetailText}>查看企业详情</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="企业图谱"
        showBackButton
        onBackPress={() => navigation.goBack()}
      />

      {/* 标签切换 */}
      <View style={styles.tabContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {(
            [
              '企业图谱',
              '关系图谱',
              '股权穿透图',
              '企业合作关系图',
            ] as TabType[]
          ).map(tab => (
            <TouchableOpacity
              key={tab}
              style={[styles.tab, activeTab === tab && styles.activeTab]}
              onPress={() => setActiveTab(tab)}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === tab && styles.activeTabText,
                ]}
              >
                {tab}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* 右侧文档按钮 */}
        <TouchableOpacity style={styles.docButton}>
          <Text style={styles.docButtonText}>📄</Text>
        </TouchableOpacity>
      </View>

      {/* 图谱内容 */}
      <ScrollView style={styles.content}>
        {activeTab === '企业图谱' && renderGraphRelationships()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    alignItems: 'center',
  },
  tab: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  activeTab: {
    backgroundColor: '#e0e8ff',
    borderRadius: 4,
  },
  tabText: {
    color: '#666',
  },
  activeTabText: {
    color: '#4169E1',
    fontWeight: 'bold',
  },
  docButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#e0e8ff',
    borderRadius: 20,
    marginHorizontal: 8,
  },
  docButtonText: {
    fontSize: 20,
  },
  content: {
    flex: 1,
  },
  graphContainer: {
    padding: 16,
  },
  mainCompanyContainer: {
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  mainCompanyBox: {
    backgroundColor: '#4169E1',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 4,
  },
  mainCompanyText: {
    color: 'white',
    fontWeight: 'bold',
  },
  relationshipsContainer: {
    marginLeft: 20,
  },
  relationshipRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  relationshipType: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginRight: 8,
  },
  relationshipTypeText: {
    fontSize: 14,
  },
  relationshipLine: {
    width: 20,
  },
  lineDash: {
    color: '#ccc',
  },
  relationshipTargets: {
    flex: 1,
  },
  targetNodeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  targetNode: {
    backgroundColor: 'white',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  targetNodeText: {
    fontSize: 14,
  },
  percentText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  checkDetailButton: {
    backgroundColor: '#4169E1',
    padding: 12,
    borderRadius: 24,
    alignItems: 'center',
    marginTop: 24,
  },
  checkDetailText: {
    color: 'white',
    fontWeight: 'bold',
  },
});

export default CompanyGraphScreen;
