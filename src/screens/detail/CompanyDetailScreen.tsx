import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
  Modal,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { DetailStackParamList } from '../../navigation/types';
import Header from '../../components/common/Header';
import Popover from '../../components/common/Popover';

type Props = NativeStackScreenProps<DetailStackParamList, 'CompanyDetail'>;

interface CategoryItem {
  id: string;
  icon: any;
  title: string;
  count?: string;
}

interface Category {
  id: string;
  title: string;
  count: string;
  items: CategoryItem[];
}

interface ContactItem {
  name?: string;
  phone: string;
  source: string;
  type?: string;
}

const CompanyDetailScreen: React.FC<Props> = ({ navigation }) => {
  const [showContactModal, setShowContactModal] = useState(false);
  const [showUnfollowModal, setShowUnfollowModal] = useState(false);
  const [activeContactTab, setActiveContactTab] = useState('all');
  const [activeContactType, setActiveContactType] = useState('mobile');

  // 模拟公司数据
  const company = {
    id: '1',
    name: '小米科技有限公司',
    logo: require('../../assets/h1.png'),
    legalPerson: '创小柯',
    registeredCapital: '6000万',
    establishDate: '1998-09-09',
    address: '青海省西宁市东川工业园区金桥路38号',
    industry: '移动智能终端设备',
    website: 'www.qimofang.com',
    stats: {
      follows: '1.2万',
      views: '277',
    },
    employeeCount: '277',
    type: '有限责任公司',
  };

  // 模拟联系方式数据
  const contactItems: ContactItem[] = [
    { name: '黎莉莉', phone: '185****2958', source: '18年年报' },
    { name: '黎莉莉', phone: '185****2958', source: '18年年报' },
    { name: '黎莉莉', phone: '185****2958', source: '18年年报' },
    { name: '黎莉莉', phone: '185****2958', source: '18年年报' },
    { name: '黎莉莉', phone: '185****2958', source: '18年年报' },
    { name: '黎莉莉', phone: '185****2958', source: '18年年报' },
  ];

  // 模拟关联联系方式数据
  const relatedContactItems: ContactItem[] = [
    { phone: '185****2958', source: '18年年报', type: '同董监高号码' },
    { phone: '185****2958', source: '18年年报', type: '同董监高号码' },
    { phone: '185****2958', source: '18年年报', type: '投资关系' },
    { phone: '185****2958', source: '18年年报', type: '投资关系' },
    { phone: '185****2958', source: '18年年报', type: '投资关系' },
  ];

  // 模拟邮箱联系方式数据
  const emailContactItems: ContactItem[] = [
    { phone: '185****<EMAIL>', source: '18年年报' },
    { phone: '185****<EMAIL>', source: '18年年报' },
    { phone: '185****<EMAIL>', source: '18年年报' },
    { phone: '185****<EMAIL>', source: '18年年报' },
    { phone: '185****<EMAIL>', source: '18年年报' },
    { phone: '185****<EMAIL>', source: '18年年报' },
  ];

  // 模拟分类数据
  const categories: Category[] = [
    {
      id: 'engineering',
      title: '工商信息',
      count: '1288',
      items: [
        {
          id: 'basic',
          title: '基本信息',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'shareholder',
          title: '股东信息',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'investment',
          title: '对外投资',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'changes',
          title: '变更记录',
          icon: require('../../assets/h1.png'),
        },
      ],
    },
    {
      id: 'risk',
      title: '风险信息',
      count: '24',
      items: [
        { id: 'lawsuit', title: '诉讼', icon: require('../../assets/h1.png') },
        {
          id: 'execution',
          title: '被执行',
          icon: require('../../assets/h1.png'),
        },
      ],
    },
    {
      id: 'marketing',
      title: '营销广告',
      count: '1288',
      items: [
        {
          id: 'trademark',
          title: '商标',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'product',
          title: '产品服务',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'recruitment',
          title: '招聘信息',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'certificate',
          title: '资质证书',
          icon: require('../../assets/h1.png'),
        },
      ],
    },
    {
      id: 'construction',
      title: '建设信息',
      count: '1288',
      items: [
        {
          id: 'project',
          title: '建设工程',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'bidding',
          title: '招投标',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'qualification',
          title: '资质证书',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'personnel',
          title: '人员',
          icon: require('../../assets/h1.png'),
        },
      ],
    },
    {
      id: 'market',
      title: '上市信息',
      count: '1288',
      items: [
        {
          id: 'announcement',
          title: '公告',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'report',
          title: '定期报告',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'shareholder-change',
          title: '股东变动',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'senior-management',
          title: '高管信息',
          icon: require('../../assets/h1.png'),
        },
      ],
    },
    {
      id: 'enterprise',
      title: '企业发展',
      count: '1288',
      items: [
        {
          id: 'financing',
          title: '融资信息',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'investment-event',
          title: '投资事件',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'product-info',
          title: '产品信息',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'core-team',
          title: '核心团队',
          icon: require('../../assets/h1.png'),
        },
      ],
    },
    {
      id: 'innovation',
      title: '创新发展',
      count: '1288',
      items: [
        { id: 'patent', title: '专利', icon: require('../../assets/h1.png') },
        { id: 'software', title: '软著', icon: require('../../assets/h1.png') },
        {
          id: 'copyright',
          title: '著作权',
          icon: require('../../assets/h1.png'),
        },
        { id: 'domain', title: '域名', icon: require('../../assets/h1.png') },
      ],
    },
    {
      id: 'property',
      title: '知识产权',
      count: '1288',
      items: [
        {
          id: 'patent-info',
          title: '专利信息',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'trademark-info',
          title: '商标信息',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'software-copyright',
          title: '软件著作权',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'work-copyright',
          title: '作品著作权',
          icon: require('../../assets/h1.png'),
        },
      ],
    },
    {
      id: 'enterprise-data',
      title: '企业数据',
      count: '1288',
      items: [
        {
          id: 'product-data',
          title: '产品数据',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'user-data',
          title: '用户数据',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'traffic-data',
          title: '流量数据',
          icon: require('../../assets/h1.png'),
        },
        {
          id: 'app-data',
          title: 'APP数据',
          icon: require('../../assets/h1.png'),
        },
      ],
    },
  ];

  const handleBack = () => {
    navigation.goBack();
  };

  const handleItemPress = (categoryId: string, itemId: string) => {
    console.log(`Pressed ${categoryId} - ${itemId}`);
  };

  const handleContact = () => {
    setShowContactModal(true);
  };

  const handleUnfollow = () => {
    setShowUnfollowModal(true);
  };

  const handleConfirmUnfollow = () => {
    console.log('Unfollow confirmed');
    setShowUnfollowModal(false);
  };

  const handleCancelUnfollow = () => {
    setShowUnfollowModal(false);
  };

  const handleCloseContactModal = () => {
    setShowContactModal(false);
  };

  const handleContactTabChange = (tab: string) => {
    setActiveContactTab(tab);
  };

  const handleContactTypeChange = (type: string) => {
    setActiveContactType(type);
  };

  const handleCallPhone = (phone: string) => {
    console.log(`Calling ${phone}`);
  };

  // 渲染网格菜单项
  const renderGridItem = (item: CategoryItem, categoryId: string) => (
    <TouchableOpacity
      key={item.id}
      className="items-center justify-center w-1/4 py-3"
      onPress={() => handleItemPress(categoryId, item.id)}
    >
      <View className="items-center">
        <Image
          source={item.icon}
          className="w-6 h-6 mb-1"
          resizeMode="contain"
        />
        <Text className="text-xs text-gray-600">{item.title}</Text>
        {item.count && (
          <Text className="text-xs text-gray-400">{item.count}</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  // 渲染分类部分
  const renderCategory = (category: Category) => (
    <View key={category.id} className="mb-2 bg-white">
      <View className="flex-row justify-between items-center px-4 py-3 border-b border-gray-100">
        <Text className="font-medium">
          {category.title} ({category.count})
        </Text>
        {category.id === 'enterprise' && (
          <Text className="text-xs text-gray-400">2022年第四季度 ▼</Text>
        )}
      </View>
      <View className="flex-row flex-wrap">
        {category.items.map(item => renderGridItem(item, category.id))}
      </View>
    </View>
  );

  // 渲染联系人项
  const renderContactItem = (item: ContactItem, index: number) => {
    let contactContent;

    if (activeContactTab === 'all') {
      contactContent = (
        <View className="flex-row justify-between items-center py-4 px-4 border-b border-gray-100">
          <View>
            <View className="flex-row items-center">
              <Text className="text-base text-gray-800">{item.name} </Text>
              <Text className="text-base text-gray-800">{item.phone}</Text>
            </View>
            <Text className="text-xs text-gray-400 mt-1">
              来源: {item.source}
            </Text>
          </View>
          <TouchableOpacity
            className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center"
            onPress={() => handleCallPhone(item.phone)}
          >
            <Image
              source={require('../../assets/h1.png')}
              className="w-5 h-5"
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      );
    } else if (activeContactTab === 'related') {
      contactContent = (
        <View className="flex-row justify-between items-center py-4 px-4 border-b border-gray-100">
          <View className="flex-1">
            <Text className="text-base text-gray-800">{item.phone}</Text>
            <Text className="text-xs text-gray-400 mt-1">
              来源: {item.source}
            </Text>
          </View>
          {item.type && (
            <View
              className={`px-2 py-1 rounded-sm mr-3 ${
                item.type === '投资关系' ? 'bg-orange-100' : 'bg-blue-100'
              }`}
            >
              <Text
                className={`text-xs ${
                  item.type === '投资关系' ? 'text-orange-500' : 'text-blue-500'
                }`}
              >
                {item.type} <Text className="text-gray-400">?</Text>
              </Text>
            </View>
          )}
          <TouchableOpacity
            className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center"
            onPress={() => handleCallPhone(item.phone)}
          >
            <Image
              source={require('../../assets/h1.png')}
              className="w-5 h-5"
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      );
    } else if (activeContactTab === 'email') {
      contactContent = (
        <View className="flex-row justify-between items-center py-4 px-4 border-b border-gray-100">
          <View>
            <Text className="text-base text-gray-800">{item.phone}</Text>
            <Text className="text-xs text-gray-400 mt-1">
              来源: {item.source}
            </Text>
          </View>
          <TouchableOpacity
            className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center"
            onPress={() => handleCallPhone(item.phone)}
          >
            <Image
              source={require('../../assets/h1.png')}
              className="w-5 h-5"
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      );
    }

    return <View key={index}>{contactContent}</View>;
  };

  // 渲染联系方式模态框
  const renderContactModal = () => {
    let contactData: ContactItem[] = [];

    if (activeContactTab === 'all') {
      contactData = contactItems;
    } else if (activeContactTab === 'related') {
      contactData = relatedContactItems;
    } else if (activeContactTab === 'email') {
      contactData = emailContactItems;
    }

    return (
      <Popover
        visible={showContactModal}
        onClose={handleCloseContactModal}
        position="bottom"
      >
        <View className="bg-white rounded-t-3xl">
          <View className="flex-row justify-center relative pt-4 pb-2">
            <Text className="text-lg font-medium">联系方式</Text>
            <TouchableOpacity
              className="absolute right-4 top-3"
              onPress={handleCloseContactModal}
            >
              <Text className="text-gray-500 text-2xl">×</Text>
            </TouchableOpacity>
          </View>

          {/* 联系方式标签页 */}
          <View className="flex-row border-b border-gray-100">
            <TouchableOpacity
              className="flex-1 py-3"
              onPress={() => handleContactTabChange('all')}
            >
              <Text
                className={`text-center ${
                  activeContactTab === 'all' ? 'text-blue-500' : 'text-gray-500'
                }`}
              >
                所有联系方式(28)
              </Text>
              {activeContactTab === 'all' && (
                <View className="h-1 bg-blue-500 absolute bottom-0 left-10 right-10" />
              )}
            </TouchableOpacity>
            <TouchableOpacity
              className="flex-1 py-3"
              onPress={() => handleContactTabChange('related')}
            >
              <Text
                className={`text-center ${
                  activeContactTab === 'related'
                    ? 'text-blue-500'
                    : 'text-gray-500'
                }`}
              >
                关联联系方式(8)
              </Text>
              {activeContactTab === 'related' && (
                <View className="h-1 bg-blue-500 absolute bottom-0 left-10 right-10" />
              )}
            </TouchableOpacity>
          </View>

          {/* 联系方式类型选择 */}
          <View className="flex-row px-4 py-3">
            <TouchableOpacity
              className={`px-6 py-2 rounded-full mr-2 ${
                activeContactType === 'mobile' ? 'bg-blue-500' : 'bg-gray-100'
              }`}
              onPress={() => handleContactTypeChange('mobile')}
            >
              <Text
                className={
                  activeContactType === 'mobile'
                    ? 'text-white'
                    : 'text-gray-500'
                }
              >
                手机(4)
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              className={`px-6 py-2 rounded-full mr-2 ${
                activeContactType === 'landline' ? 'bg-blue-500' : 'bg-gray-100'
              }`}
              onPress={() => handleContactTypeChange('landline')}
            >
              <Text
                className={
                  activeContactType === 'landline'
                    ? 'text-white'
                    : 'text-gray-500'
                }
              >
                固话(4)
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              className={`px-6 py-2 rounded-full ${
                activeContactType === 'email' ? 'bg-blue-500' : 'bg-gray-100'
              }`}
              onPress={() => handleContactTypeChange('email')}
            >
              <Text
                className={
                  activeContactType === 'email' ? 'text-white' : 'text-gray-500'
                }
              >
                邮箱(4)
              </Text>
            </TouchableOpacity>
          </View>

          {/* 联系人列表 */}
          <ScrollView className="flex-1">
            {contactData.map((item, index) => renderContactItem(item, index))}
          </ScrollView>

          {/* 底部指示器 */}
          <View className="items-center pb-8 pt-2">
            <View className="w-16 h-1 bg-gray-300 rounded-full" />
          </View>
        </View>
      </Popover>
    );
  };

  // 渲染取消关注确认框
  const renderUnfollowModal = () => (
    <Modal
      animationType="fade"
      transparent={true}
      visible={showUnfollowModal}
      onRequestClose={handleCancelUnfollow}
    >
      <View className="flex-1 justify-center items-center bg-[rgba(0,0,0,0.5)]">
        <View className="bg-white rounded-lg w-10/12 p-5">
          <View className="items-center mb-5">
            <View className="w-16 h-16 bg-blue-100 rounded-full items-center justify-center mb-3">
              <Image
                source={require('../../assets/h1.png')}
                className="w-8 h-8"
                resizeMode="contain"
              />
            </View>
            <Text className="text-xl font-medium">温馨提示</Text>
          </View>

          <Text className="text-center text-gray-700 mb-5">
            取消关注xxxxx 公司后{'\n'}
            将无法及时获取该公司的最新相关信息
          </Text>

          <View className="flex-row">
            <TouchableOpacity
              className="flex-1 border border-gray-300 py-3 rounded-md items-center mr-2"
              onPress={handleCancelUnfollow}
            >
              <Text className="text-gray-500">取消</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className="flex-1 bg-blue-500 py-3 rounded-md items-center"
              onPress={handleConfirmUnfollow}
            >
              <Text className="text-white">确定</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-100">
      <Header title="企业详情" showBackButton onBackPress={handleBack} />

      <ScrollView className="flex-1">
        {/* 公司基本信息 */}
        <View className="bg-white p-4 mb-2">
          <View className="flex-row mb-4">
            <Image
              source={company.logo}
              className="w-12 h-12 rounded"
              resizeMode="contain"
            />
            <View className="ml-3 flex-1">
              <Text className="text-lg font-medium">{company.name}</Text>
              <View className="flex-row flex-wrap mt-1">
                <View className="bg-orange-100 rounded-sm px-1 mr-1 mb-1">
                  <Text className="text-orange-500 text-xs">大型</Text>
                </View>
                <View className="bg-blue-100 rounded-sm px-1 mr-1 mb-1">
                  <Text className="text-blue-500 text-xs">A股</Text>
                </View>
                <View className="bg-green-100 rounded-sm px-1 mr-1 mb-1">
                  <Text className="text-green-500 text-xs">500强</Text>
                </View>
                <View className="bg-blue-100 rounded-sm px-1 mr-1 mb-1">
                  <Text className="text-blue-500 text-xs">国家高新技术</Text>
                </View>
              </View>
            </View>
            <TouchableOpacity>
              <Text className="text-blue-500">▼</Text>
            </TouchableOpacity>
          </View>

          <View className="flex-row justify-between py-2 border-b border-gray-100">
            <Text className="text-gray-500">企业法人</Text>
            <Text className="text-gray-700">{company.legalPerson}</Text>
          </View>

          <View className="flex-row justify-between py-2 border-b border-gray-100">
            <Text className="text-gray-500">注册资本</Text>
            <Text className="text-gray-700">{company.registeredCapital}</Text>
          </View>

          <View className="flex-row justify-between py-2 border-b border-gray-100">
            <Text className="text-gray-500">注册日期</Text>
            <Text className="text-gray-700">{company.establishDate}</Text>
          </View>

          <View className="flex-row justify-between py-2 border-b border-gray-100">
            <Text className="text-gray-500">企业类型</Text>
            <Text className="text-gray-700">{company.type}</Text>
          </View>

          <View className="flex-row justify-between py-2 border-b border-gray-100">
            <Text className="text-gray-500">所属行业</Text>
            <Text className="text-gray-700">{company.industry}</Text>
          </View>

          <View className="flex-row justify-between py-2">
            <Text className="text-gray-500">员工人数</Text>
            <Text className="text-gray-700">{company.employeeCount}</Text>
          </View>
        </View>

        {/* 公司详细信息 */}
        <View className="bg-blue-50 p-4 mb-2">
          <View className="mb-2">
            <Text className="text-gray-500">企业地址：{company.address}</Text>
          </View>
          <View className="mb-2">
            <Text className="text-gray-500">所属行业：{company.industry}</Text>
          </View>
          <View className="flex-row items-center">
            <Text className="text-gray-500">企业官网：</Text>
            <Text className="text-blue-500">{company.website}</Text>
          </View>
        </View>

        {/* 统计信息 */}
        <View className="flex-row bg-white py-3 mb-2">
          <View className="flex-1 items-center border-r border-gray-200">
            <Text className="text-xs text-gray-400">关注</Text>
            <Text className="text-blue-500 mt-1">{company.stats.follows}</Text>
          </View>
          <View className="flex-1 items-center">
            <Text className="text-xs text-gray-400">浏览</Text>
            <Text className="text-blue-500 mt-1">{company.stats.views}</Text>
          </View>
        </View>

        {/* 菜单分类 */}
        {categories.map(renderCategory)}

        {/* 底部按钮 */}
        <View className="flex-row px-4 py-4">
          <TouchableOpacity
            className="flex-1 bg-blue-500 py-3 rounded-md items-center mr-2"
            onPress={handleContact}
          >
            <Text className="text-white font-medium">联系方式</Text>
          </TouchableOpacity>
          <TouchableOpacity
            className="flex-1 border border-gray-300 py-3 rounded-md items-center"
            onPress={handleUnfollow}
          >
            <Text className="text-gray-500 font-medium">取消关注</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* 联系方式模态框 */}
      {renderContactModal()}

      {/* 取消关注确认框 */}
      {renderUnfollowModal()}
    </SafeAreaView>
  );
};

export default CompanyDetailScreen;
