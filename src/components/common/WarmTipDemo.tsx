import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import Button from './Button';
import WarmTip from './WarmTip';
import { useSimpleWarmTip } from '../../hooks/useWarmTip';

const WarmTipDemo: React.FC = () => {
  const {
    showSimpleTip,
    showSuccessTip,
    showErrorTip,
    showWarningTip,
    tipProps,
  } = useSimpleWarmTip();

  const handleAgreementTip = () => {
    showSimpleTip(
      '已阅读并同意《中国联通认证服务条款》以及《启魔方用户协议》和《启魔方免责声明》',
      () => {
        console.log('用户确认了协议');
        // 这里可以执行确认后的逻辑
      }
    );
  };

  const handleSuccessDemo = () => {
    showSuccessTip('您的设置已保存成功！');
  };

  const handleErrorDemo = () => {
    showErrorTip('网络连接失败，请检查网络设置');
  };

  const handleWarningDemo = () => {
    showWarningTip(
      '此操作将删除所有数据，确定要继续吗？',
      () => {
        console.log('用户确认了警告操作');
        // 执行删除操作
      }
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>温馨提示组件演示</Text>
        <Text style={styles.description}>
          这是一个从底部弹出的温馨提示组件，支持多种配置选项和动画效果。
        </Text>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>基础用法</Text>
          <Button
            title="显示协议提示"
            onPress={handleAgreementTip}
            gradient={true}
            style={styles.button}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>快捷方法</Text>
          
          <Button
            title="成功提示（自动关闭）"
            onPress={handleSuccessDemo}
            type="primary"
            style={styles.button}
          />

          <Button
            title="错误提示"
            onPress={handleErrorDemo}
            type="secondary"
            style={styles.button}
          />

          <Button
            title="警告提示（不可点击背景关闭）"
            onPress={handleWarningDemo}
            type="outline"
            style={styles.button}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>特性说明</Text>
          <View style={styles.featureList}>
            <Text style={styles.featureItem}>• 从底部平滑弹出动画</Text>
            <Text style={styles.featureItem}>• 支持自定义标题和内容</Text>
            <Text style={styles.featureItem}>• 可配置确认按钮文字</Text>
            <Text style={styles.featureItem}>• 支持自动关闭功能</Text>
            <Text style={styles.featureItem}>• 可控制是否显示关闭按钮</Text>
            <Text style={styles.featureItem}>• 可设置点击背景是否关闭</Text>
            <Text style={styles.featureItem}>• 使用 react-native-reanimated 实现流畅动画</Text>
          </View>
        </View>
      </View>

      {/* 温馨提示组件 */}
      <WarmTip {...tipProps} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
  },
  button: {
    marginBottom: 12,
  },
  featureList: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  featureItem: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
    lineHeight: 20,
  },
});

export default WarmTipDemo;
