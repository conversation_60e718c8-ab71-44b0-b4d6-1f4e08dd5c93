import React, { useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Dimensions,
  TouchableWithoutFeedback,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import CloseIcon from '../../assets/svg/close.svg';
import Button from './Button';

const { height: screenHeight } = Dimensions.get('window');

export interface WarmTipProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  content: string;
  confirmText?: string;
  onConfirm?: () => void;
  showCloseButton?: boolean;
  closeOnBackdropPress?: boolean;
  autoClose?: boolean;
  autoCloseDelay?: number;
}

const WarmTip: React.FC<WarmTipProps> = ({
  visible,
  onClose,
  title = '温馨提示',
  content,
  confirmText = '我知道了',
  onConfirm,
  showCloseButton = true,
  closeOnBackdropPress = true,
  autoClose = false,
  autoCloseDelay = 3000,
}) => {
  const translateY = useSharedValue(screenHeight);
  const opacity = useSharedValue(0);

  useEffect(() => {
    if (visible) {
      // 显示动画
      opacity.value = withTiming(1, { duration: 300 });
      translateY.value = withSpring(0, {
        damping: 20,
        stiffness: 90,
      });

      // 自动关闭
      if (autoClose) {
        const timer = setTimeout(() => {
          handleClose();
        }, autoCloseDelay);
        return () => clearTimeout(timer);
      }
    } else {
      // 隐藏动画
      translateY.value = withTiming(screenHeight, { duration: 250 });
      opacity.value = withTiming(0, { duration: 250 });
    }
  }, [visible, autoClose, autoCloseDelay]);

  const handleClose = () => {
    translateY.value = withTiming(screenHeight, { duration: 250 });
    opacity.value = withTiming(0, { duration: 250 }, () => {
      runOnJS(onClose)();
    });
  };

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    }
    handleClose();
  };

  const handleBackdropPress = () => {
    if (closeOnBackdropPress) {
      handleClose();
    }
  };

  const backdropAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
    };
  });

  const contentAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={handleClose}
      statusBarTranslucent
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <Animated.View style={[styles.backdrop, backdropAnimatedStyle]}>
          <TouchableWithoutFeedback>
            <Animated.View style={[styles.container, contentAnimatedStyle]}>
              {/* 头部 */}
              <View style={styles.header}>
                <Text style={styles.title}>{title}</Text>
                {showCloseButton && (
                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={handleClose}
                    hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                  >
                    <CloseIcon width={20} height={20} fill="#9CA3AF" />
                  </TouchableOpacity>
                )}
              </View>

              {/* 内容 */}
              <View style={styles.content}>
                <Text style={styles.contentText}>{content}</Text>
              </View>

              {/* 确认按钮 */}
              <View style={styles.buttonContainer}>
                <Button
                  title={confirmText}
                  onPress={handleConfirm}
                  gradient={true}
                  borderRadius={8}
                  style={styles.confirmButton}
                />
              </View>
            </Animated.View>
          </TouchableWithoutFeedback>
        </Animated.View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'transparent',
    justifyContent: 'flex-end',
  },
  container: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 24,
    paddingTop: 24,
    paddingBottom: 34,
    minHeight: 200,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'center',
    flex: 1,
  },
  closeButton: {
    position: 'absolute',
    right: 0,
    top: -2,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    marginBottom: 32,
  },
  contentText: {
    fontSize: 16,
    lineHeight: 24,
    color: '#6B7280',
    textAlign: 'left',
  },
  buttonContainer: {
    width: '100%',
  },
  confirmButton: {
    width: '100%',
  },
});

export default WarmTip;
