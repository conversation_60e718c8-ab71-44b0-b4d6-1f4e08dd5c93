import React from 'react';
import { View, TextInput, TouchableOpacity, Text } from 'react-native';

interface SearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  onSearch: () => void;
  onClear?: () => void;
  placeholder?: string;
  autoFocus?: boolean;
  returnKeyType?: 'search' | 'done' | 'go' | 'next';
  showSearchButton?: boolean;
  searchButtonText?: string;
  onPressSearchBar?: () => void; // 新增点击搜索框回调
}

const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChangeText,
  onSearch,
  onClear,
  placeholder = '输入关键词搜索',
  autoFocus = false,
  returnKeyType = 'search',
  showSearchButton = true,
  searchButtonText = '搜索',
  onPressSearchBar,
}) => {
  if (onPressSearchBar) {
    // 不可编辑模式，用于首页点击跳转到搜索页
    return (
      <TouchableOpacity
        className="flex-row items-center mx-4 my-2 bg-white rounded-full px-4 py-2 border border-gray-200"
        onPress={onPressSearchBar}
      >
        <Text className="text-gray-400 mr-2">🔍</Text>
        <Text className="flex-1 text-gray-400 text-center">{placeholder}</Text>
      </TouchableOpacity>
    );
  }

  return (
    <View className="flex-row items-center px-4 py-2 bg-gray-50">
      <View className="flex-row items-center flex-1 bg-white rounded-full px-4 py-1 border border-gray-200">
        <Text className="text-gray-400 mr-2">🔍</Text>
        <TextInput
          className="flex-1 h-10"
          placeholder={placeholder}
          value={value}
          onChangeText={onChangeText}
          returnKeyType={returnKeyType}
          onSubmitEditing={onSearch}
          autoFocus={autoFocus}
        />
        {value.length > 0 && onClear && (
          <TouchableOpacity onPress={onClear}>
            <Text className="text-gray-400">✕</Text>
          </TouchableOpacity>
        )}
      </View>
      {showSearchButton && (
        <TouchableOpacity className="ml-3 px-3 py-2" onPress={onSearch}>
          <Text className="text-blue-500 font-medium">{searchButtonText}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default SearchBar;
