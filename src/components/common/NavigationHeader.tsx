import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleProp,
  ViewStyle,
  TextStyle,
  StatusBar,
  Platform,
  StyleSheet,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import BackIcon from '../../assets/svg/back.svg';

interface NavigationHeaderProps {
  // 标题相关
  title?: string;
  titleStyle?: StyleProp<TextStyle>;
  titleAlign?: 'left' | 'center';

  // 返回按钮相关
  showBackButton?: boolean;
  onBackPress?: () => void;
  backButtonStyle?: StyleProp<ViewStyle>;
  customBackButton?: React.ReactNode;
  backButtonColor?: string;

  // 右侧组件
  rightComponent?: React.ReactNode;

  // 样式相关
  containerStyle?: StyleProp<ViewStyle>;
  backgroundColor?: string;
  borderBottomWidth?: number;
  borderBottomColor?: string;

  // 安全距离相关
  includeSafeArea?: boolean;
  statusBarStyle?: 'default' | 'light-content' | 'dark-content';
  statusBarBackgroundColor?: string;

  // 高度相关
  height?: number;

  // 其他组件
  leftComponent?: React.ReactNode; // 除了返回按钮外的左侧组件
  centerComponent?: React.ReactNode; // 替代标题的中间组件
}

const NavigationHeader: React.FC<NavigationHeaderProps> = ({
  title,
  titleStyle,
  titleAlign = 'center',
  showBackButton = true,
  onBackPress,
  backButtonStyle,
  customBackButton,
  backButtonColor = '#374151',
  rightComponent,
  containerStyle,
  backgroundColor = '#ffffff',
  borderBottomWidth = 1,
  borderBottomColor = '#e5e7eb',
  includeSafeArea = true,
  statusBarStyle = 'dark-content',
  statusBarBackgroundColor,
  height = 40,
  leftComponent,
  centerComponent,
}) => {
  const insets = useSafeAreaInsets();

  // 默认返回按钮
  const defaultBackButton = (
    <TouchableOpacity
      onPress={onBackPress}
      style={[styles.backButton, backButtonStyle]}
      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
    >
      <View style={styles.backIconContainer}>
        <BackIcon width={18} height={18} fill={backButtonColor} />
      </View>
    </TouchableOpacity>
  );

  // 渲染左侧内容
  const renderLeftContent = () => {
    if (leftComponent) {
      return leftComponent;
    }

    if (showBackButton) {
      return customBackButton || defaultBackButton;
    }

    return null;
  };

  // 渲染中间内容
  const renderCenterContent = () => {
    if (centerComponent) {
      return centerComponent;
    }

    if (title) {
      return (
        <Text
          style={[
            styles.titleText,
            titleAlign === 'center'
              ? styles.titleTextCenter
              : styles.titleTextLeft,
            titleStyle,
          ]}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {title}
        </Text>
      );
    }

    return null;
  };

  // 计算容器样式
  const getContainerStyle = (): StyleProp<ViewStyle> => {
    const totalHeight = includeSafeArea ? height + insets.top : height;
    const baseStyle: ViewStyle = {
      backgroundColor,
      borderBottomWidth,
      borderBottomColor,
      height: totalHeight,
      paddingTop: includeSafeArea ? insets.top : 0,
    };

    return [baseStyle, containerStyle];
  };

  return (
    <>
      {/* 状态栏 */}
      <StatusBar
        barStyle={statusBarStyle}
        backgroundColor={statusBarBackgroundColor || backgroundColor}
        translucent={Platform.OS === 'android'}
      />

      {/* 导航头容器 */}
      <View style={getContainerStyle()}>
        <View style={[styles.contentContainer, { height }]}>
          {/* 左侧内容 */}
          <View style={styles.leftContainer}>{renderLeftContent()}</View>

          {/* 中间内容 */}
          <View
            style={[
              styles.centerContainer,
              titleAlign === 'center'
                ? styles.centerContainerCenter
                : styles.centerContainerLeft,
            ]}
          >
            {renderCenterContent()}
          </View>

          {/* 右侧内容 */}
          <View style={styles.rightContainer}>
            {rightComponent || <View style={styles.rightPlaceholder} />}
          </View>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexShrink: 0,
  },
  centerContainer: {
    flex: 1,
    marginHorizontal: 16,
  },
  centerContainerLeft: {
    alignItems: 'flex-start',
  },
  centerContainerCenter: {
    alignItems: 'center',
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexShrink: 0,
  },
  rightPlaceholder: {
    width: 32,
  },
  backButton: {
    paddingLeft: 7,
  },
  backIconContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#1f2937',
  },
  titleTextCenter: {
    textAlign: 'center',
  },
  titleTextLeft: {
    textAlign: 'left',
  },
});

export default NavigationHeader;
