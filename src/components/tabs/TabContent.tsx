import React from 'react';
import { View } from 'react-native';

export interface TabContentProps {
  activeTab: string;
  tabId: string;
  children: React.ReactNode;
}

const TabContent: React.FC<TabContentProps> = ({
  activeTab,
  tabId,
  children,
}) => {
  if (activeTab !== tabId) {
    return null;
  }

  return <View className="flex-1 bg-white">{children}</View>;
};

export default TabContent;
